package handlers

import (
	"fmt"
	"strings"
	"time"

	"vulnerability_push/internal/models"
	"vulnerability_push/push"
)

// 推送单个IOC到指定通道
func (h *IOCHandler) pushSingleIOCToChannel(iocIntel *models.IOCIntelligence, channel *models.PushChannel) error {
	// 创建推送记录
	record := models.IOCIntelligencePushRecord{
		IOCIntelligenceID: iocIntel.ID,
		ChannelID:         channel.ID,
		ChannelName:       channel.Name,
		ChannelType:       channel.Type,
		Status:            "success",
		PushedAt:          time.Now().Unix(),
	}

	// 转换为push包中的类型
	pushIOCIntel := h.convertToIOCIntelligencePush(iocIntel)
	pushChannel := h.convertToPushChannel(channel)
	pushRecord := h.convertToIOCIntelligencePushRecord(&record)

	// 根据通道类型推送
	var pushErr error
	switch channel.Type {
	case "wechat_bot":
		pushErr = h.pushIOCIntelligenceToWechatBot(pushIOCIntel, pushChannel, pushRecord)
	case "dingding":
		pushErr = h.pushIOCIntelligenceToDingDing(pushIOCIntel, pushChannel, pushRecord)
	case "webhook":
		pushErr = h.pushIOCIntelligenceToWebhook(pushIOCIntel, pushChannel, pushRecord)
	case "lark":
		pushErr = h.pushIOCIntelligenceToLark(pushIOCIntel, pushChannel, pushRecord)
	default:
		pushErr = fmt.Errorf("不支持的通道类型: %s", channel.Type)
	}

	// 更新推送记录状态
	if pushErr != nil {
		record.Status = "failed"
		record.ErrorMessage = pushErr.Error()
	}

	// 保存推送记录
	if err := h.db.Create(&record).Error; err != nil {
		fmt.Printf("保存推送记录失败: %v\n", err)
	}

	return pushErr
}

// 批量推送IOC情报到指定通道
func (h *IOCHandler) batchPushIOCIntelligenceToChannel(iocIntels []models.IOCIntelligence, channel *models.PushChannel) error {
	// 转换为push包中的类型
	pushIOCIntels := make([]*push.IOCIntelligence, 0, len(iocIntels))
	for _, ioc := range iocIntels {
		pushIOCIntels = append(pushIOCIntels, h.convertToIOCIntelligencePush(&ioc))
	}

	pushChannel := h.convertToPushChannel(channel)

	// 根据通道类型批量推送
	var pushErr error
	switch channel.Type {
	case "wechat_bot":
		pushErr = push.BatchPushIOCIntelligenceToWechatBot(pushIOCIntels, pushChannel)
	case "dingding":
		pushErr = push.BatchPushIOCIntelligenceToDingDing(pushIOCIntels, pushChannel)
	case "webhook":
		pushErr = push.BatchPushIOCIntelligenceToWebhook(pushIOCIntels, pushChannel)
	case "lark":
		pushErr = push.BatchPushIOCIntelligenceToLark(pushIOCIntels, pushChannel)
	default:
		pushErr = fmt.Errorf("不支持的通道类型: %s", channel.Type)
	}

	// 创建批量推送记录并更新IOC情报状态
	for i, ioc := range iocIntels {
		record := models.IOCIntelligencePushRecord{
			IOCIntelligenceID: ioc.ID,
			ChannelID:         channel.ID,
			ChannelName:       channel.Name,
			ChannelType:       channel.Type,
			Status:            "success",
			PushedAt:          time.Now().Unix(),
		}

		// 更新IOC情报的推送状态
		if pushErr != nil {
			record.Status = "failed"
			record.ErrorMessage = pushErr.Error()
			// 推送失败，标记为失败状态
			iocIntels[i].MarkAsPushFailed()
		} else {
			// 推送成功，标记为已推送状态
			iocIntels[i].MarkAsPushed()
		}

		// 保存IOC情报状态更新
		if err := h.db.Save(&iocIntels[i]).Error; err != nil {
			fmt.Printf("更新IOC情报推送状态失败: %v\n", err)
		}

		// 保存推送记录
		if err := h.db.Create(&record).Error; err != nil {
			// 如果表不存在，尝试创建表
			if strings.Contains(err.Error(), "doesn't exist") {
				h.db.AutoMigrate(&models.IOCIntelligencePushRecord{})
				// 重新尝试保存
				if err := h.db.Create(&record).Error; err != nil {
					fmt.Printf("保存推送记录失败: %v\n", err)
				}
			} else {
				fmt.Printf("保存推送记录失败: %v\n", err)
			}
		}
	}

	return pushErr
}

// 批量推送IOC情报使用策略
func (h *IOCHandler) batchPushIOCIntelligenceUsingPolicy(iocIntels []models.IOCIntelligence, policy *models.PushPolicy) error {
	if policy.ChannelIDs == "" {
		return fmt.Errorf("策略 %s 没有配置推送通道", policy.Name)
	}

	// 解析通道ID列表
	channelIDs := strings.Split(policy.ChannelIDs, ",")

	var errors []string
	for _, channelIDStr := range channelIDs {
		channelIDStr = strings.TrimSpace(channelIDStr)
		if channelIDStr == "" {
			continue
		}

		var channelID uint
		fmt.Sscanf(channelIDStr, "%d", &channelID)

		var channel models.PushChannel
		if err := h.db.First(&channel, channelID).Error; err != nil {
			errors = append(errors, fmt.Sprintf("通道ID %s 不存在", channelIDStr))
			continue
		}

		if !channel.Status {
			errors = append(errors, fmt.Sprintf("通道 %s 已禁用", channel.Name))
			continue
		}

		// 批量推送到该通道
		if err := h.batchPushIOCIntelligenceToChannel(iocIntels, &channel); err != nil {
			errors = append(errors, fmt.Sprintf("推送到通道 %s 失败: %v", channel.Name, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分推送失败: %s", strings.Join(errors, "; "))
	}

	return nil
}

// 批量推送IOC情报到默认通道
func (h *IOCHandler) batchPushIOCIntelligenceToDefault(iocIntels []models.IOCIntelligence) error {
	// 获取所有启用的推送通道
	var channels []models.PushChannel
	if err := h.db.Where("status = ?", true).Find(&channels).Error; err != nil {
		return fmt.Errorf("获取推送通道失败: %v", err)
	}

	if len(channels) == 0 {
		return fmt.Errorf("没有可用的推送通道")
	}

	var errors []string
	for _, channel := range channels {
		// 批量推送到该通道
		if err := h.batchPushIOCIntelligenceToChannel(iocIntels, &channel); err != nil {
			errors = append(errors, fmt.Sprintf("推送到通道 %s 失败: %v", channel.Name, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分推送失败: %s", strings.Join(errors, "; "))
	}

	return nil
}
