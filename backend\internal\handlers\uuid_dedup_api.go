package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"vulnerability_push/internal/models"
)

// UUIDDedupAPIHandler UUID去重API处理器
type UUIDDedupAPIHandler struct {
	*BaseHandler
	db            *gorm.DB
	configManager *UUIDDedupConfigManager
	benchmark     *UUIDDedupBenchmark
}

// NewUUIDDedupAPIHandler 创建UUID去重API处理器
func NewUUIDDedupAPIHandler(db *gorm.DB) *UUIDDedupAPIHandler {
	benchmark := NewUUIDDedupBenchmark(db)

	return &UUIDDedupAPIHandler{
		BaseHandler:   NewBaseHandler(),
		db:            db,
		configManager: GlobalUUIDDedupConfigManager,
		benchmark:     benchmark,
	}
}

// GetConfigs 获取所有UUID去重配置
func (h *UUIDDedupAPIHandler) GetConfigs(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	configs := h.configManager.ListConfigs()
	
	h.Success(c, gin.H{
		"configs": configs,
		"current_cpu_count": h.configManager.generateAutoConfig().WorkerCount,
	})
}

// GetConfig 获取指定配置
func (h *UUIDDedupAPIHandler) GetConfig(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	configName := c.Param("name")
	if configName == "" {
		h.BadRequest(c, "配置名称不能为空")
		return
	}

	config := h.configManager.GetConfig(configName)
	h.configManager.PrintConfigInfo(configName)
	
	h.Success(c, gin.H{
		"config": config,
		"name":   configName,
	})
}

// SetCustomConfig 设置自定义配置
func (h *UUIDDedupAPIHandler) SetCustomConfig(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req struct {
		Name          string `json:"name" binding:"required"`
		BatchSize     int    `json:"batch_size" binding:"required,min=1"`
		WorkerCount   int    `json:"worker_count" binding:"required,min=1"`
		SaveBatchSize int    `json:"save_batch_size" binding:"required,min=1"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	config := &UUIDDedupConfig{
		BatchSize:     req.BatchSize,
		WorkerCount:   req.WorkerCount,
		SaveBatchSize: req.SaveBatchSize,
	}

	// 验证配置
	if err := h.configManager.ValidateConfig(config); err != nil {
		h.BadRequest(c, "配置验证失败: "+err.Error())
		return
	}

	h.configManager.SetCustomConfig(req.Name, config)
	
	h.Success(c, gin.H{
		"message": "自定义配置设置成功",
		"config":  config,
	})
}

// GetRecommendedConfig 根据数据量获取推荐配置
func (h *UUIDDedupAPIHandler) GetRecommendedConfig(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	dataSizeStr := c.Query("data_size")
	if dataSizeStr == "" {
		h.BadRequest(c, "数据量参数不能为空")
		return
	}

	dataSize, err := strconv.Atoi(dataSizeStr)
	if err != nil || dataSize <= 0 {
		h.BadRequest(c, "数据量参数无效")
		return
	}

	config := h.configManager.GetRecommendedConfig(dataSize)
	
	h.Success(c, gin.H{
		"data_size": dataSize,
		"config":    config,
		"message":   "推荐配置获取成功",
	})
}

// RunBenchmark 运行UUID去重性能测试
func (h *UUIDDedupAPIHandler) RunBenchmark(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req struct {
		DataSize      int     `json:"data_size" binding:"required,min=1,max=100000"`
		ExistingRatio float64 `json:"existing_ratio" binding:"min=0,max=1"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 设置默认值
	if req.ExistingRatio == 0 {
		req.ExistingRatio = 0.3 // 默认30%的UUID已存在
	}

	// 运行性能测试
	results, err := h.benchmark.RunBenchmark(req.DataSize, req.ExistingRatio)
	if err != nil {
		h.InternalServerError(c, "性能测试失败: "+err.Error())
		return
	}

	// 打印结果到控制台
	h.benchmark.PrintBenchmarkResults(results)

	h.Success(c, gin.H{
		"message":        "性能测试完成",
		"data_size":      req.DataSize,
		"existing_ratio": req.ExistingRatio,
		"results":        results,
	})
}

// GetPerformanceStats 获取UUID去重性能统计
func (h *UUIDDedupAPIHandler) GetPerformanceStats(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	// 这里可以添加实际的性能统计逻辑
	// 目前返回示例数据
	stats := gin.H{
		"total_processed_today": 0,
		"average_processing_time": "0ms",
		"current_config": h.configManager.GetConfig("auto"),
		"system_info": gin.H{
			"cpu_count": h.configManager.generateAutoConfig().WorkerCount,
			"memory_usage": "N/A",
		},
	}

	h.Success(c, stats)
}

// ClearUUIDCache 清除UUID缓存（测试用）
func (h *UUIDDedupAPIHandler) ClearUUIDCache(c *gin.Context) {
	if !h.RequireAdmin(c) {
		return
	}

	sourceType := c.Query("source_type")
	if sourceType == "" {
		sourceType = "benchmark_test" // 默认只清除测试数据
	}

	// 清除指定类型的UUID记录
	result := h.db.Where("source_type = ?", sourceType).Delete(&models.ProcessedUUID{})
	if result.Error != nil {
		h.InternalServerError(c, "清除UUID缓存失败: "+result.Error.Error())
		return
	}

	h.Success(c, gin.H{
		"message":       "UUID缓存清除成功",
		"source_type":   sourceType,
		"deleted_count": result.RowsAffected,
	})
}

// RegisterUUIDDedupRoutes 注册UUID去重相关路由
func RegisterUUIDDedupRoutes(router *gin.RouterGroup, db *gorm.DB) {
	handler := NewUUIDDedupAPIHandler(db)
	
	uuidGroup := router.Group("/uuid-dedup")
	{
		// 配置管理
		uuidGroup.GET("/configs", handler.GetConfigs)
		uuidGroup.GET("/configs/:name", handler.GetConfig)
		uuidGroup.POST("/configs", handler.SetCustomConfig)
		uuidGroup.GET("/recommend", handler.GetRecommendedConfig)
		
		// 性能测试
		uuidGroup.POST("/benchmark", handler.RunBenchmark)
		uuidGroup.GET("/stats", handler.GetPerformanceStats)
		
		// 管理功能
		uuidGroup.DELETE("/cache", handler.ClearUUIDCache)
	}
}
