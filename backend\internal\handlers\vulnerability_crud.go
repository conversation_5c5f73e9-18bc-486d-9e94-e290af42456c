package handlers

import (
	"errors"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"vulnerability_push/internal/models"
)

// UpdateVulnerabilityRequest 更新漏洞请求
type UpdateVulnerabilityRequest struct {
	Name           string   `json:"name" binding:"required,max=200"`
	VulnID         string   `json:"vulnId" binding:"required,max=100"`
	Severity       string   `json:"severity" binding:"required,oneof=低危 中危 高危 严重"`
	Tags           []string `json:"tags"`
	DisclosureDate string   `json:"disclosureDate" binding:"required"`
	Source         string   `json:"source" binding:"required,max=100"`
	Description    string   `json:"description" binding:"required"`
	References     []string `json:"references"`
	Remediation    string   `json:"remediation" binding:"required"`
	PushReason     string   `json:"pushReason"`
}

// UpdateVulnerability 更新漏洞
func (h *VulnerabilityHandler) UpdateVulnerability(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var uriReq GetVulnerabilityRequest
	if err := c.ShouldBindUri(&uriReq); err != nil {
		h.ValidationError(c, err)
		return
	}

	var req UpdateVulnerabilityRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 查找要更新的漏洞
	var vulnerability models.Vulnerability
	if err := h.db.First(&vulnerability, uriReq.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			h.NotFound(c, "漏洞不存在")
			return
		}
		h.InternalServerError(c, "查找漏洞失败: "+err.Error())
		return
	}

	// 检查漏洞ID是否与其他漏洞冲突
	if req.VulnID != vulnerability.VulnID {
		var existingVuln models.Vulnerability
		if err := h.db.Where("vuln_id = ? AND id != ?", req.VulnID, uriReq.ID).First(&existingVuln).Error; err == nil {
			h.BadRequest(c, "漏洞ID已存在")
			return
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			h.InternalServerError(c, "检查漏洞ID失败: "+err.Error())
			return
		}
	}

	// 更新漏洞信息
	vulnerability.Name = req.Name
	vulnerability.VulnID = req.VulnID
	vulnerability.Severity = req.Severity
	vulnerability.Tags = strings.Join(req.Tags, ",")
	vulnerability.DisclosureDate = req.DisclosureDate
	vulnerability.Source = req.Source
	vulnerability.Description = req.Description
	vulnerability.References = strings.Join(req.References, ",")
	vulnerability.Remediation = req.Remediation
	vulnerability.PushReason = req.PushReason

	// 保存到数据库
	if err := h.db.Save(&vulnerability).Error; err != nil {
		h.InternalServerError(c, "更新漏洞失败: "+err.Error())
		return
	}

	// 返回更新后的漏洞信息
	responseData := map[string]interface{}{
		"id":             vulnerability.ID,
		"name":           vulnerability.Name,
		"vulnId":         vulnerability.VulnID,
		"severity":       vulnerability.Severity,
		"tags":           strings.Split(vulnerability.Tags, ","),
		"disclosureDate": vulnerability.DisclosureDate,
		"source":         vulnerability.Source,
		"description":    vulnerability.Description,
		"references":     strings.Split(vulnerability.References, ","),
		"remediation":    vulnerability.Remediation,
		"createdAt":      vulnerability.CreatedAt,
	}

	h.SuccessWithMessage(c, "漏洞更新成功", responseData)
}

// DeleteVulnerabilityRequest 删除漏洞请求
type DeleteVulnerabilityRequest struct {
	ID uint `uri:"id" binding:"required"`
}

// DeleteVulnerability 删除漏洞
func (h *VulnerabilityHandler) DeleteVulnerability(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req DeleteVulnerabilityRequest
	if err := c.ShouldBindUri(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 检查漏洞是否存在
	var vulnerability models.Vulnerability
	if err := h.db.First(&vulnerability, req.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			h.NotFound(c, "漏洞不存在")
			return
		}
		h.InternalServerError(c, "查找漏洞失败: "+err.Error())
		return
	}

	// 删除漏洞
	if err := h.db.Delete(&vulnerability).Error; err != nil {
		h.InternalServerError(c, "删除漏洞失败: "+err.Error())
		return
	}

	h.SuccessWithMessage(c, "漏洞删除成功", nil)
}

// BatchDeleteVulnerabilitiesRequest 批量删除漏洞请求
type BatchDeleteVulnerabilitiesRequest struct {
	IDs []uint `json:"ids" binding:"required"`
}

// BatchDeleteVulnerabilities 批量删除漏洞
func (h *VulnerabilityHandler) BatchDeleteVulnerabilities(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req BatchDeleteVulnerabilitiesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	if len(req.IDs) == 0 {
		h.BadRequest(c, "请选择要删除的漏洞")
		return
	}

	// 批量删除
	result := h.db.Where("id IN ?", req.IDs).Delete(&models.Vulnerability{})
	if result.Error != nil {
		h.InternalServerError(c, "批量删除失败: "+result.Error.Error())
		return
	}

	h.SuccessWithMessage(c, "批量删除成功", map[string]interface{}{
		"deletedCount": result.RowsAffected,
	})
}

// PushVulnerabilityRequest 推送漏洞请求
type PushVulnerabilityRequest struct {
	ID        uint   `uri:"id" binding:"required"`
	ChannelID uint   `json:"channelId" binding:"required"`
	Reason    string `json:"reason"`
}

// PushVulnerability 手动推送漏洞
func (h *VulnerabilityHandler) PushVulnerability(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var uriReq struct {
		ID uint `uri:"id" binding:"required"`
	}
	if err := c.ShouldBindUri(&uriReq); err != nil {
		h.ValidationError(c, err)
		return
	}

	var req struct {
		ChannelID uint   `json:"channelId" binding:"required"`
		Reason    string `json:"reason"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 查找漏洞
	var vulnerability models.Vulnerability
	if err := h.db.First(&vulnerability, uriReq.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			h.NotFound(c, "漏洞不存在")
			return
		}
		h.InternalServerError(c, "查找漏洞失败: "+err.Error())
		return
	}

	// 查找推送通道
	var channel models.PushChannel
	if err := h.db.First(&channel, req.ChannelID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			h.NotFound(c, "推送通道不存在")
			return
		}
		h.InternalServerError(c, "查找推送通道失败: "+err.Error())
		return
	}

	// 检查通道是否启用
	if !channel.IsEnabled {
		h.BadRequest(c, "推送通道已禁用")
		return
	}

	// 创建推送记录
	record := &models.PushRecord{
		VulnerabilityID: vulnerability.ID,
		ChannelID:       channel.ID,
		Status:          "pending",
		CreatedAt:       time.Now().Unix(),
	}

	if err := h.db.Create(record).Error; err != nil {
		h.InternalServerError(c, "创建推送记录失败: "+err.Error())
		return
	}

	// 执行推送（异步）
	go func() {
		// 这里应该调用推送服务
		// TODO: 实现具体的推送逻辑
		
		// 更新推送记录状态
		record.Status = "success"
		record.PushedAt = time.Now().Unix()
		h.db.Save(record)
	}()

	h.SuccessWithMessage(c, "推送任务已创建", map[string]interface{}{
		"recordId": record.ID,
	})
}

// BatchPushVulnerabilitiesRequest 批量推送漏洞请求
type BatchPushVulnerabilitiesRequest struct {
	IDs       []uint `json:"ids" binding:"required"`
	ChannelID uint   `json:"channelId" binding:"required"`
	Reason    string `json:"reason"`
}

// BatchPushVulnerabilities 批量推送漏洞
func (h *VulnerabilityHandler) BatchPushVulnerabilities(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req BatchPushVulnerabilitiesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	if len(req.IDs) == 0 {
		h.BadRequest(c, "请选择要推送的漏洞")
		return
	}

	// 查找推送通道
	var channel models.PushChannel
	if err := h.db.First(&channel, req.ChannelID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			h.NotFound(c, "推送通道不存在")
			return
		}
		h.InternalServerError(c, "查找推送通道失败: "+err.Error())
		return
	}

	// 检查通道是否启用
	if !channel.IsEnabled {
		h.BadRequest(c, "推送通道已禁用")
		return
	}

	// 查找要推送的漏洞
	var vulnerabilities []models.Vulnerability
	if err := h.db.Where("id IN ?", req.IDs).Find(&vulnerabilities).Error; err != nil {
		h.InternalServerError(c, "查找漏洞失败: "+err.Error())
		return
	}

	if len(vulnerabilities) == 0 {
		h.BadRequest(c, "未找到要推送的漏洞")
		return
	}

	// 创建推送记录
	var records []models.PushRecord
	for _, vuln := range vulnerabilities {
		record := models.PushRecord{
			VulnerabilityID: vuln.ID,
			ChannelID:       channel.ID,
			Status:          "pending",
			CreatedAt:       time.Now().Unix(),
		}
		records = append(records, record)
	}

	if err := h.db.Create(&records).Error; err != nil {
		h.InternalServerError(c, "创建推送记录失败: "+err.Error())
		return
	}

	// 执行批量推送（异步）
	go func() {
		// TODO: 实现具体的批量推送逻辑
		for i := range records {
			records[i].Status = "success"
			records[i].PushedAt = time.Now().Unix()
		}
		h.db.Save(&records)
	}()

	h.SuccessWithMessage(c, "批量推送任务已创建", map[string]interface{}{
		"count": len(records),
	})
}
