package handlers

import (
	"encoding/xml"
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"vulnerability_push/internal/models"
)

// UpdateRssConfigRequest 更新RSS配置请求
type UpdateRssConfigRequest struct {
	Enabled         bool   `json:"enabled"`
	RequireAuth     bool   `json:"requireAuth"`
	Title           string `json:"title"`
	Description     string `json:"description"`
	ItemCount       int    `json:"itemCount"`
	IncludeSeverity string `json:"includeSeverity"`
	ExcludeTags     string `json:"excludeTags"`
}

// RSS XML结构定义
type RSSFeed struct {
	XMLName xml.Name   `xml:"rss"`
	Version string     `xml:"version,attr"`
	Channel RSSChannel `xml:"channel"`
}

type RSSChannel struct {
	XMLName       xml.Name  `xml:"channel"`
	Title         string    `xml:"title"`
	Link          string    `xml:"link"`
	Description   string    `xml:"description"`
	Language      string    `xml:"language"`
	LastBuildDate string    `xml:"lastBuildDate"`
	Items         []RSSItem `xml:"item"`
}

type RSSItem struct {
	Title       string `xml:"title"`
	Link        string `xml:"link"`
	Description string `xml:"description"`
	PubDate     string `xml:"pubDate"`
	GUID        string `xml:"guid"`
	Category    string `xml:"category"`
}

// GetRssConfig 获取RSS配置
func (h *PushHandler) GetRssConfig(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var config models.RssConfig
	result := h.db.First(&config)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// 如果没有找到配置，则返回默认配置
			config = models.RssConfig{
				Enabled:         true,
				RequireAuth:     false,
				Title:           "漏洞情报管理平台 - 漏洞订阅",
				Description:     "最新安全漏洞信息订阅",
				ItemCount:       50,
				IncludeSeverity: "严重,高危,中危,低危",
				ExcludeTags:     "",
			}
			// 创建默认配置
			if err := h.db.Create(&config).Error; err != nil {
				h.InternalServerError(c, "创建默认RSS配置失败: "+err.Error())
				return
			}
		} else {
			h.InternalServerError(c, "获取RSS配置失败: "+result.Error.Error())
			return
		}
	}

	h.Success(c, config)
}

// UpdateRssConfig 更新RSS配置
func (h *PushHandler) UpdateRssConfig(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req UpdateRssConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 验证参数
	if req.Title == "" {
		h.BadRequest(c, "RSS标题不能为空")
		return
	}
	if req.ItemCount <= 0 || req.ItemCount > 200 {
		h.BadRequest(c, "RSS条目数量必须在1-200之间")
		return
	}

	var config models.RssConfig
	result := h.db.First(&config)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// 如果不存在则创建
			config = models.RssConfig{
				Enabled:         req.Enabled,
				RequireAuth:     req.RequireAuth,
				Title:           req.Title,
				Description:     req.Description,
				ItemCount:       req.ItemCount,
				IncludeSeverity: req.IncludeSeverity,
				ExcludeTags:     req.ExcludeTags,
			}
			if err := h.db.Create(&config).Error; err != nil {
				h.InternalServerError(c, "创建RSS配置失败: "+err.Error())
				return
			}
		} else {
			h.InternalServerError(c, "获取RSS配置失败: "+result.Error.Error())
			return
		}
	} else {
		// 更新现有记录
		config.Enabled = req.Enabled
		config.RequireAuth = req.RequireAuth
		config.Title = req.Title
		config.Description = req.Description
		config.ItemCount = req.ItemCount
		config.IncludeSeverity = req.IncludeSeverity
		config.ExcludeTags = req.ExcludeTags

		if err := h.db.Save(&config).Error; err != nil {
			h.InternalServerError(c, "更新RSS配置失败: "+err.Error())
			return
		}
	}

	h.SuccessWithMessage(c, "更新RSS配置成功", config)
}

// GenerateRSS 生成RSS订阅
func (h *PushHandler) GenerateRSS(c *gin.Context) {
	// 获取RSS配置
	var config models.RssConfig
	if err := h.db.First(&config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(404, gin.H{"error": "RSS配置不存在"})
			return
		}
		c.JSON(500, gin.H{"error": "获取RSS配置失败"})
		return
	}

	// 检查RSS是否启用
	if !config.Enabled {
		c.JSON(403, gin.H{"error": "RSS订阅已禁用"})
		return
	}

	// 检查是否需要认证
	if config.RequireAuth {
		apiKey := c.Query("key")
		if apiKey == "" {
			c.JSON(401, gin.H{"error": "需要API密钥"})
			return
		}

		// 验证API密钥
		var user models.User
		if err := h.db.Where("api_key = ?", apiKey).First(&user).Error; err != nil {
			c.JSON(401, gin.H{"error": "无效的API密钥"})
			return
		}
	}

	// 构建漏洞查询
	query := h.db.Model(&models.Vulnerability{})

	// 应用严重程度过滤
	if config.IncludeSeverity != "" {
		severities := strings.Split(config.IncludeSeverity, ",")
		for i, severity := range severities {
			severities[i] = strings.TrimSpace(severity)
		}
		query = query.Where("severity IN ?", severities)
	}

	// 应用标签排除过滤
	if config.ExcludeTags != "" {
		excludeTags := strings.Split(config.ExcludeTags, ",")
		for _, tag := range excludeTags {
			tag = strings.TrimSpace(tag)
			if tag != "" {
				query = query.Where("tags NOT LIKE ?", "%"+tag+"%")
			}
		}
	}

	// 获取漏洞列表
	var vulnerabilities []models.Vulnerability
	if err := query.Order("created_at DESC").Limit(config.ItemCount).Find(&vulnerabilities).Error; err != nil {
		c.JSON(500, gin.H{"error": "获取漏洞数据失败"})
		return
	}

	// 构建基础URL
	scheme := "http"
	if c.Request.TLS != nil {
		scheme = "https"
	}
	baseURL := c.Request.Host
	if baseURL == "" {
		baseURL = "localhost:55555"
	}
	baseURL = fmt.Sprintf("%s://%s", scheme, baseURL)

	// 创建RSS Feed
	feed := RSSFeed{
		Version: "2.0",
		Channel: RSSChannel{
			Title:         config.Title,
			Link:          baseURL,
			Description:   config.Description,
			Language:      "zh-cn",
			LastBuildDate: time.Now().Format(time.RFC1123Z),
			Items:         make([]RSSItem, 0),
		},
	}

	// 添加漏洞条目
	for _, vuln := range vulnerabilities {
		pubDate := time.Unix(vuln.CreatedAt, 0).Format(time.RFC1123Z)
		link := fmt.Sprintf("%s/vulnerabilities/%d", baseURL, vuln.ID)

		// 构建描述
		description := fmt.Sprintf("<p><strong>漏洞编号:</strong> %s</p>", vuln.VulnID)
		description += fmt.Sprintf("<p><strong>严重程度:</strong> %s</p>", vuln.Severity)
		if vuln.DisclosureDate != "" {
			description += fmt.Sprintf("<p><strong>披露日期:</strong> %s</p>", vuln.DisclosureDate)
		}
		if vuln.Description != "" {
			description += fmt.Sprintf("<p><strong>描述:</strong> %s</p>", vuln.Description)
		}
		if vuln.Remediation != "" {
			description += fmt.Sprintf("<p><strong>修复建议:</strong> %s</p>", vuln.Remediation)
		}

		// 添加标签
		if vuln.Tags != "" {
			tags := strings.Split(vuln.Tags, ",")
			if len(tags) > 0 {
				description += "<p><strong>标签:</strong> "
				for i, tag := range tags {
					tag = strings.TrimSpace(tag)
					if i > 0 {
						description += ", "
					}
					description += tag
				}
				description += "</p>"
			}
		}

		// 添加参考链接
		if vuln.References != "" {
			refs := strings.Split(vuln.References, ",")
			if len(refs) > 0 {
				description += "<p><strong>参考链接:</strong></p><ul>"
				for _, ref := range refs {
					ref = strings.TrimSpace(ref)
					if ref != "" {
						description += fmt.Sprintf("<li><a href=\"%s\">%s</a></li>", ref, ref)
					}
				}
				description += "</ul>"
			}
		}

		item := RSSItem{
			Title:       vuln.Name,
			Link:        link,
			Description: description,
			PubDate:     pubDate,
			GUID:        link,
			Category:    vuln.Severity,
		}

		feed.Channel.Items = append(feed.Channel.Items, item)
	}

	// 设置响应头
	c.Header("Content-Type", "application/rss+xml; charset=utf-8")

	// 生成XML
	output, err := xml.MarshalIndent(feed, "", "  ")
	if err != nil {
		c.JSON(500, gin.H{"error": "生成RSS失败"})
		return
	}

	// 添加XML声明
	xmlHeader := []byte("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n")
	output = append(xmlHeader, output...)

	c.Data(200, "application/rss+xml", output)
}
