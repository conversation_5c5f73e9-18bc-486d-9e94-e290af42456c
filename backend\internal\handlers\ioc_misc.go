package handlers

import (
	"encoding/csv"
	"fmt"
	"io"
	"mime/multipart"
	"net"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"vulnerability_push/internal/models"
	"vulnerability_push/internal/service"
	"vulnerability_push/internal/utils"
)

// GetIOCWhitelistRequest 获取IOC白名单请求
type GetIOCWhitelistRequest struct {
	Page     int    `form:"page"`
	PageSize int    `form:"page_size"`
	IOC      string `form:"ioc"`
	IOCType  string `form:"ioc_type"`
	OrderBy  string `form:"order_by"`
	OrderDir string `form:"order_dir"`
}

// NormalizePagination 标准化分页参数
func (req *GetIOCWhitelistRequest) NormalizePagination() {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}
}

// GetIOCWhitelist 获取IOC白名单列表
func (h *IOCHandler) GetIOCWhitelist(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req GetIOCWhitelistRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 标准化分页参数
	req.NormalizePagination()
	if req.OrderBy == "" {
		req.OrderBy = "created_at"
	}
	if req.OrderDir == "" {
		req.OrderDir = "desc"
	}

	// 构建查询
	query := h.db.Model(&models.IOCWhitelist{})

	// 应用过滤条件
	if req.IOC != "" {
		query = query.Where("ioc LIKE ?", "%"+req.IOC+"%")
	}
	if req.IOCType != "" {
		query = query.Where("ioc_type = ?", req.IOCType)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取IOC白名单总数失败: "+err.Error())
		return
	}

	// 应用排序和分页
	var whitelist []models.IOCWhitelist
	offset := (req.Page - 1) * req.PageSize
	orderClause := req.OrderBy + " " + req.OrderDir

	if err := query.Order(orderClause).Offset(offset).Limit(req.PageSize).Find(&whitelist).Error; err != nil {
		h.InternalServerError(c, "获取IOC白名单列表失败: "+err.Error())
		return
	}

	h.PaginatedSuccess(c, whitelist, total, req.Page, req.PageSize)
}

// DownloadIOCWhitelistTemplate 下载IOC白名单导入模板
func (h *IOCHandler) DownloadIOCWhitelistTemplate(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	// 创建CSV模板内容
	csvContent := `IOC值,IOC类型,备注
***********,ip,单个IPv4地址示例
2001:db8::1,ip,单个IPv6地址示例
***********/24,ip,IPv4 CIDR网段示例
2001:db8::/32,ip,IPv6 CIDR网段示例
***********-100,ip,IPv4数字范围示例
***********-*************,ip,IPv4完整范围示例
2001:db8::1-2001:db8::10,ip,IPv6范围示例
example.com,domain,域名示例
*.malicious.com,domain,通配符域名示例`

	// 设置响应头
	c.Header("Content-Type", "text/csv; charset=utf-8")
	c.Header("Content-Disposition", "attachment; filename=ioc_whitelist_template.csv")
	c.Header("Cache-Control", "no-cache")

	// 添加UTF-8 BOM，确保Excel能正确识别中文
	c.Writer.Write([]byte{0xEF, 0xBB, 0xBF})

	// 返回CSV内容
	c.String(200, csvContent)
}

// BatchImportIOCWhitelistRequest 批量导入IOC白名单请求
type BatchImportIOCWhitelistRequest struct {
	File *multipart.FileHeader `form:"file" binding:"required"`
}

// BatchImportIOCWhitelist 批量导入IOC白名单
func (h *IOCHandler) BatchImportIOCWhitelist(c *gin.Context) {
	if !h.RequireAdmin(c) {
		return
	}

	// 设置较长的超时时间用于大文件处理
	c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, 50<<20) // 50MB限制

	// 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		if strings.Contains(err.Error(), "request body too large") {
			h.BadRequest(c, "文件过大，最大支持50MB")
			return
		}
		h.BadRequest(c, "文件上传失败: "+err.Error())
		return
	}
	defer file.Close()

	// 检查文件大小
	if header.Size > 50<<20 { // 50MB
		h.BadRequest(c, "文件过大，最大支持50MB")
		return
	}

	// 检查文件类型
	filename := header.Filename
	if !strings.HasSuffix(strings.ToLower(filename), ".csv") {
		h.BadRequest(c, "只支持CSV格式文件")
		return
	}

	// 获取当前用户信息
	var createdBy string
	userInfo, exists := c.Get("user")
	if exists {
		if user, ok := userInfo.(*models.User); ok {
			createdBy = user.Username
		}
	}
	if createdBy == "" {
		createdBy = "system"
	}

	// 解析CSV文件
	reader := csv.NewReader(file)
	reader.FieldsPerRecord = -1 // 允许不同行有不同的字段数
	reader.LazyQuotes = true    // 允许懒惰引号处理
	reader.TrimLeadingSpace = true // 自动去除前导空格

	var whitelists []models.IOCWhitelist
	var errors []string
	var warnings []string
	lineNum := 0
	processedCount := 0
	skippedCount := 0
	maxErrors := 100 // 最大错误数限制

	// 添加处理进度跟踪
	const batchSize = 1000 // 每批处理的记录数

	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			lineNum++
			if len(errors) < maxErrors {
				errors = append(errors, fmt.Sprintf("第%d行读取失败: %v", lineNum, err))
			}
			continue
		}

		lineNum++

		// 跳过标题行
		if lineNum == 1 {
			continue
		}

		// 限制处理的总行数，防止内存溢出
		if lineNum > 100000 { // 最大10万行
			warnings = append(warnings, "文件行数过多，只处理前100000行数据")
			break
		}

		// 检查字段数量
		if len(record) < 2 {
			if len(errors) < maxErrors {
				errors = append(errors, fmt.Sprintf("第%d行字段数量不足，至少需要IOC值和IOC类型", lineNum))
			}
			continue
		}

		// 解析字段
		ioc := strings.TrimSpace(record[0])
		iocType := strings.TrimSpace(record[1])
		var remark string
		if len(record) > 2 {
			remark = strings.TrimSpace(record[2])
		}

		// 验证必填字段
		if ioc == "" {
			if len(errors) < maxErrors {
				errors = append(errors, fmt.Sprintf("第%d行IOC值不能为空", lineNum))
			}
			continue
		}
		if iocType == "" {
			if len(errors) < maxErrors {
				errors = append(errors, fmt.Sprintf("第%d行IOC类型不能为空", lineNum))
			}
			continue
		}

		// 验证IOC类型
		if iocType != "ip" && iocType != "domain" {
			if len(errors) < maxErrors {
				errors = append(errors, fmt.Sprintf("第%d行IOC类型无效，只支持ip或domain", lineNum))
			}
			continue
		}

		// 验证IOC值长度
		if len(ioc) > 255 {
			if len(errors) < maxErrors {
				errors = append(errors, fmt.Sprintf("第%d行IOC值过长，最大支持255个字符", lineNum))
			}
			continue
		}

		// 验证备注长度
		if len(remark) > 500 {
			remark = remark[:500] // 截断过长的备注
			if len(warnings) < 50 {
				warnings = append(warnings, fmt.Sprintf("第%d行备注过长，已截断至500字符", lineNum))
			}
		}

		// 处理IP范围格式
		if iocType == "ip" {
			// 尝试解析IP范围
			ipList, err := h.parseIPRange(ioc)
			if err != nil {
				if len(errors) < maxErrors {
					errors = append(errors, fmt.Sprintf("第%d行IP范围解析失败: %v", lineNum, err))
				}
				continue
			}

			// 检查IP数量限制
			if len(ipList) > 10000 {
				if len(errors) < maxErrors {
					errors = append(errors, fmt.Sprintf("第%d行IP范围过大，单个范围最多支持10000个IP地址", lineNum))
				}
				continue
			}

			// 为每个IP创建白名单记录
			for _, ip := range ipList {
				// 检查是否已存在（批量检查优化）
				var existingCount int64
				if err := h.db.Model(&models.IOCWhitelist{}).Where("ioc = ?", ip).Count(&existingCount).Error; err != nil {
					// 数据库查询错误，记录警告但继续处理
					if len(warnings) < 50 {
						warnings = append(warnings, fmt.Sprintf("第%d行检查IP %s 是否存在时出错: %v", lineNum, ip, err))
					}
				}

				if existingCount > 0 {
					skippedCount++
					continue // 跳过已存在的IP
				}

				whitelist := models.IOCWhitelist{
					IOC:       ip,
					IOCType:   iocType,
					Remark:    remark,
					CreatedBy: createdBy,
				}
				whitelists = append(whitelists, whitelist)
				processedCount++

				// 分批处理，避免内存溢出
				if len(whitelists) >= batchSize {
					if err := h.batchCreateWhitelists(whitelists); err != nil {
						if len(errors) < maxErrors {
							errors = append(errors, fmt.Sprintf("批量创建白名单记录失败: %v", err))
						}
						return
					}
					whitelists = whitelists[:0] // 清空切片但保留容量
				}
			}
		} else {
			// 域名类型，直接验证格式
			if err := h.validateIOCFormat(ioc, iocType); err != nil {
				if len(errors) < maxErrors {
					errors = append(errors, fmt.Sprintf("第%d行IOC格式错误: %v", lineNum, err))
				}
				continue
			}

			// 检查是否已存在
			var existingCount int64
			if err := h.db.Model(&models.IOCWhitelist{}).Where("ioc = ?", ioc).Count(&existingCount).Error; err != nil {
				if len(warnings) < 50 {
					warnings = append(warnings, fmt.Sprintf("第%d行检查域名 %s 是否存在时出错: %v", lineNum, ioc, err))
				}
			}

			if existingCount > 0 {
				skippedCount++
				continue // 跳过已存在的域名
			}

			whitelist := models.IOCWhitelist{
				IOC:       ioc,
				IOCType:   iocType,
				Remark:    remark,
				CreatedBy: createdBy,
			}
			whitelists = append(whitelists, whitelist)
			processedCount++

			// 分批处理
			if len(whitelists) >= batchSize {
				if err := h.batchCreateWhitelists(whitelists); err != nil {
					if len(errors) < maxErrors {
						errors = append(errors, fmt.Sprintf("批量创建白名单记录失败: %v", err))
					}
					return
				}
				whitelists = whitelists[:0]
			}
		}

		// 检查是否有太多错误
		if len(errors) >= maxErrors {
			errors = append(errors, "错误过多，停止处理后续数据")
			break
		}
	}

	// 处理剩余的记录
	if len(whitelists) > 0 {
		if err := h.batchCreateWhitelists(whitelists); err != nil {
			errors = append(errors, fmt.Sprintf("批量创建剩余白名单记录失败: %v", err))
		}
	}

	// 构建响应消息
	var responseMsg strings.Builder
	var responseData = gin.H{
		"processed_count": processedCount,
		"skipped_count":   skippedCount,
		"total_lines":     lineNum - 1, // 减去标题行
		"error_count":     len(errors),
		"warning_count":   len(warnings),
	}

	if len(errors) > 0 {
		// 有错误但可能有部分成功
		if processedCount > 0 {
			responseMsg.WriteString(fmt.Sprintf("部分导入成功：处理了 %d 条记录", processedCount))
			if skippedCount > 0 {
				responseMsg.WriteString(fmt.Sprintf("，跳过 %d 条重复记录", skippedCount))
			}
			responseMsg.WriteString(fmt.Sprintf("，但有 %d 个错误", len(errors)))
		} else {
			responseMsg.WriteString("导入失败，请检查文件格式")
		}

		responseData["errors"] = errors[:min(len(errors), 20)] // 只返回前20个错误
		if len(errors) > 20 {
			responseData["errors"] = append(responseData["errors"].([]string), "...还有更多错误")
		}
	} else {
		// 完全成功
		responseMsg.WriteString(fmt.Sprintf("导入成功：处理了 %d 条记录", processedCount))
		if skippedCount > 0 {
			responseMsg.WriteString(fmt.Sprintf("，跳过 %d 条重复记录", skippedCount))
		}
	}

	// 添加警告信息
	if len(warnings) > 0 {
		responseData["warnings"] = warnings[:min(len(warnings), 10)] // 只返回前10个警告
		if len(warnings) > 10 {
			responseData["warnings"] = append(responseData["warnings"].([]string), "...还有更多警告")
		}
	}

	responseData["message"] = responseMsg.String()

	// 如果有记录被处理，刷新白名单缓存
	if processedCount > 0 {
		if err := service.RefreshGlobalWhitelistCache(); err != nil {
			utils.Errorf("刷新IOC白名单缓存失败: %v", err)
		}
	}

	// 根据是否有错误决定响应状态
	if len(errors) > 0 && processedCount == 0 {
		h.JSON(c, http.StatusBadRequest, 400, responseMsg.String(), responseData)
	} else {
		h.Success(c, responseData)
	}
}

// GetProductionStrategyConfig 获取生产策略配置
func (h *IOCHandler) GetProductionStrategyConfig(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	// 获取默认生产策略
	var strategy models.ProductionStrategy
	if err := h.db.Where("status = ?", "enabled").First(&strategy).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "未找到启用的生产策略")
			return
		}
		h.InternalServerError(c, "获取生产策略失败: "+err.Error())
		return
	}

	h.Success(c, strategy)
}

// GetIOCIntelligenceStats 获取IOC情报统计信息
func (h *IOCHandler) GetIOCIntelligenceStats(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	// 获取总IOC情报数
	var total int64
	if err := h.db.Model(&models.IOCIntelligence{}).Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取IOC情报统计失败: "+err.Error())
		return
	}

	// 获取高危IOC数量（严重和高危）
	var highRiskCount int64
	if err := h.db.Model(&models.IOCIntelligence{}).Where("risk_level IN ('严重', '高危')").Count(&highRiskCount).Error; err != nil {
		h.InternalServerError(c, "获取IOC情报统计失败: "+err.Error())
		return
	}

	// 获取今日新增IOC数量
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayStartUnix := todayStart.Unix()

	var todayNew int64
	if err := h.db.Model(&models.IOCIntelligence{}).Where("created_at >= ?", todayStartUnix).Count(&todayNew).Error; err != nil {
		h.InternalServerError(c, "获取IOC情报统计失败: "+err.Error())
		return
	}

	// 计算增长率（与上周对比）
	twoWeeksAgo := time.Now().AddDate(0, 0, -14).Unix()
	oneWeekAgo := time.Now().AddDate(0, 0, -7).Unix()

	var lastWeekCount int64
	if err := h.db.Model(&models.IOCIntelligence{}).Where("created_at >= ? AND created_at < ?", twoWeeksAgo, oneWeekAgo).Count(&lastWeekCount).Error; err != nil {
		h.InternalServerError(c, "获取IOC情报统计失败: "+err.Error())
		return
	}

	var recentCount int64
	if err := h.db.Model(&models.IOCIntelligence{}).Where("created_at >= ?", oneWeekAgo).Count(&recentCount).Error; err != nil {
		h.InternalServerError(c, "获取IOC情报统计失败: "+err.Error())
		return
	}

	var growthRate int
	if lastWeekCount > 0 {
		growthRate = int((float64(recentCount-lastWeekCount) / float64(lastWeekCount)) * 100)
	} else if recentCount > 0 {
		growthRate = 100
	} else {
		growthRate = 0
	}

	// 按风险等级统计
	var riskLevelCounts []struct {
		RiskLevel string
		Count     int64
	}
	if err := h.db.Model(&models.IOCIntelligence{}).Select("risk_level, count(*) as count").Group("risk_level").Scan(&riskLevelCounts).Error; err != nil {
		h.InternalServerError(c, "获取IOC情报统计失败: "+err.Error())
		return
	}

	riskLevelDistribution := make(map[string]int64)
	for _, rlc := range riskLevelCounts {
		riskLevelDistribution[rlc.RiskLevel] = rlc.Count
	}

	// 按IOC类型统计
	var iocTypeCounts []struct {
		IOCType string
		Count   int64
	}
	if err := h.db.Model(&models.IOCIntelligence{}).Select("ioc_type, count(*) as count").Group("ioc_type").Scan(&iocTypeCounts).Error; err != nil {
		h.InternalServerError(c, "获取IOC情报统计失败: "+err.Error())
		return
	}

	iocTypeDistribution := make(map[string]int64)
	for _, itc := range iocTypeCounts {
		iocTypeDistribution[itc.IOCType] = itc.Count
	}

	// 获取今日生产的IOC列表（最新10条）
	var todayIOCList []map[string]interface{}
	var todayIOCs []models.IOCIntelligence

	// 计算今日的时间戳范围
	todayEnd := todayStart.Add(24 * time.Hour)
	todayStartTimestamp := todayStart.Unix()
	todayEndTimestamp := todayEnd.Unix()

	if err := h.db.Model(&models.IOCIntelligence{}).
		Where("created_at >= ? AND created_at < ?", todayStartTimestamp, todayEndTimestamp).
		Order("created_at DESC").
		Limit(10).
		Find(&todayIOCs).Error; err != nil {
		h.InternalServerError(c, "获取今日IOC列表失败: "+err.Error())
		return
	}

	for _, ioc := range todayIOCs {
		todayIOCList = append(todayIOCList, map[string]interface{}{
			"id":        ioc.ID,
			"iocValue":  ioc.IOC,
			"iocType":   ioc.IOCType,
			"riskLevel": ioc.RiskLevel,
			"source":    ioc.Source,
			"createdAt": ioc.CreatedAt,
		})
	}

	// 构建响应数据
	stats := map[string]interface{}{
		"total": map[string]interface{}{
			"count":      total,
			"growthRate": growthRate,
		},
		"highRisk": map[string]interface{}{
			"count":      highRiskCount,
			"needAction": highRiskCount > 0,
		},
		"todayNew": map[string]interface{}{
			"count":  todayNew,
			"status": map[bool]string{true: "需要关注", false: "正常"}[todayNew > 0],
		},
		"riskLevelDistribution": riskLevelDistribution,
		"iocTypeDistribution":   iocTypeDistribution,
		"todayIOCList":          todayIOCList,
	}

	h.Success(c, stats)
}

// GenerateIOCFromSourceRequest 从源数据生成IOC情报请求
type GenerateIOCFromSourceRequest struct {
	StrategyID *uint  `json:"strategy_id"`
	TimeRange  string `json:"time_range"`
}

// GenerateIOCFromSource 从源数据生成IOC情报
func (h *IOCHandler) GenerateIOCFromSource(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req GenerateIOCFromSourceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// TODO: 实现从源数据生成IOC情报的逻辑
	// 1. 获取指定的生产策略（如果提供了strategy_id）
	// 2. 从IOC源数据表中获取数据
	// 3. 应用生产策略规则
	// 4. 生成IOC情报并保存

	h.Success(c, gin.H{
		"message": "从源数据生成IOC情报成功",
		"generated_count": 0, // TODO: 返回实际生成的数量
	})
}

// GetIOCIntelligencePushStats 获取IOC情报推送统计信息
func (h *IOCHandler) GetIOCIntelligencePushStats(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	// TODO: 实现IOC情报推送统计逻辑
	// 统计推送成功率、失败率、各通道推送情况等

	stats := map[string]interface{}{
		"totalPushes":    0,
		"successRate":    0.0,
		"failureRate":    0.0,
		"channelStats":   make(map[string]int64),
		"recentPushes":   0,
	}

	h.Success(c, stats)
}

// parseIPRange 解析IP范围，支持多种格式
func (h *IOCHandler) parseIPRange(ipRange string) ([]string, error) {
	var ips []string

	// 检查是否为CIDR格式
	if strings.Contains(ipRange, "/") {
		_, ipNet, err := net.ParseCIDR(ipRange)
		if err != nil {
			return nil, fmt.Errorf("无效的CIDR格式: %v", err)
		}

		// 检查CIDR范围大小
		ones, bits := ipNet.Mask.Size()
		if bits == 32 { // IPv4
			if ones < 16 { // 大于/16的网段
				return nil, fmt.Errorf("IPv4 CIDR范围过大，最小支持/16网段")
			}
		} else if bits == 128 { // IPv6
			if ones < 64 { // 大于/64的网段
				return nil, fmt.Errorf("IPv6 CIDR范围过大，最小支持/64网段")
			}
		}

		// 遍历CIDR范围内的所有IP
		for ip := ipNet.IP.Mask(ipNet.Mask); ipNet.Contains(ip); h.incrementIP(ip) {
			ips = append(ips, ip.String())
			// 限制最大IP数量，防止内存溢出
			if len(ips) > 10000 {
				return nil, fmt.Errorf("IP范围过大，最多支持10000个地址")
			}
		}
		return ips, nil
	}

	// 检查是否为范围格式 (***********-************* 或 ***********-100)
	if strings.Contains(ipRange, "-") {
		parts := strings.Split(ipRange, "-")
		if len(parts) != 2 {
			return nil, fmt.Errorf("无效的IP范围格式")
		}

		startIP := strings.TrimSpace(parts[0])
		endPart := strings.TrimSpace(parts[1])

		// 验证起始IP
		start := net.ParseIP(startIP)
		if start == nil {
			return nil, fmt.Errorf("无效的起始IP地址: %s", startIP)
		}

		var end net.IP
		// 检查结束部分是否为完整IP
		if strings.Contains(endPart, ".") || strings.Contains(endPart, ":") {
			end = net.ParseIP(endPart)
			if end == nil {
				return nil, fmt.Errorf("无效的结束IP地址: %s", endPart)
			}
		} else {
			// 数字范围格式 (如 ***********-100)
			endNum, err := strconv.Atoi(endPart)
			if err != nil {
				return nil, fmt.Errorf("无效的结束数字: %s", endPart)
			}

			// 构造结束IP
			if start.To4() != nil {
				// IPv4
				startBytes := start.To4()
				endBytes := make([]byte, 4)
				copy(endBytes, startBytes)
				endBytes[3] = byte(endNum)
				end = net.IP(endBytes)
			} else {
				return nil, fmt.Errorf("IPv6不支持数字范围格式")
			}
		}

		// 生成范围内的所有IP
		current := make(net.IP, len(start))
		copy(current, start)

		for {
			ips = append(ips, current.String())
			if current.Equal(end) {
				break
			}
			h.incrementIP(current)
			// 限制最大IP数量
			if len(ips) > 10000 {
				return nil, fmt.Errorf("IP范围过大，最多支持10000个地址")
			}
		}
		return ips, nil
	}

	// 单个IP地址
	ip := net.ParseIP(ipRange)
	if ip == nil {
		return nil, fmt.Errorf("无效的IP地址: %s", ipRange)
	}

	return []string{ip.String()}, nil
}

// batchCreateWhitelists 批量创建白名单记录
func (h *IOCHandler) batchCreateWhitelists(whitelists []models.IOCWhitelist) error {
	if len(whitelists) == 0 {
		return nil
	}

	// 使用事务确保数据一致性
	return h.db.Transaction(func(tx *gorm.DB) error {
		// 分批插入，每批100条记录
		batchSize := 100
		for i := 0; i < len(whitelists); i += batchSize {
			end := i + batchSize
			if end > len(whitelists) {
				end = len(whitelists)
			}

			batch := whitelists[i:end]
			if err := tx.CreateInBatches(batch, batchSize).Error; err != nil {
				return fmt.Errorf("批量插入第 %d-%d 条记录失败: %v", i+1, end, err)
			}
		}
		return nil
	})
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// incrementIP 递增IP地址
func (h *IOCHandler) incrementIP(ip net.IP) {
	for j := len(ip) - 1; j >= 0; j-- {
		ip[j]++
		if ip[j] > 0 {
			break
		}
	}
}

// validateIOCFormat 验证IOC格式
func (h *IOCHandler) validateIOCFormat(ioc, iocType string) error {
	switch iocType {
	case "ip":
		// IP地址验证
		if net.ParseIP(ioc) == nil {
			return fmt.Errorf("无效的IP地址格式")
		}
	case "domain":
		// 域名验证
		if !h.isValidDomain(ioc) {
			return fmt.Errorf("无效的域名格式")
		}
	default:
		return fmt.Errorf("不支持的IOC类型: %s", iocType)
	}
	return nil
}

// isValidDomain 验证域名格式
func (h *IOCHandler) isValidDomain(domain string) bool {
	// 基本长度检查
	if len(domain) == 0 || len(domain) > 253 {
		return false
	}

	// 支持通配符域名
	if strings.HasPrefix(domain, "*.") {
		domain = domain[2:]
	}

	// 域名正则表达式
	domainRegex := regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$`)
	return domainRegex.MatchString(domain)
}

// CreateIOCWhitelistRequest 创建IOC白名单请求
type CreateIOCWhitelistRequest struct {
	IOC     string `json:"ioc" binding:"required"`
	IOCType string `json:"iocType" binding:"required"`
	Remark  string `json:"remark"`
}

// CreateIOCWhitelist 创建IOC白名单
func (h *IOCHandler) CreateIOCWhitelist(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req CreateIOCWhitelistRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 验证IOC类型
	if req.IOCType != "ip" && req.IOCType != "domain" {
		h.BadRequest(c, "无效的IOC类型，只支持ip和domain")
		return
	}

	// 验证IOC格式
	if err := h.validateIOCFormat(req.IOC, req.IOCType); err != nil {
		h.BadRequest(c, err.Error())
		return
	}

	// 获取当前用户
	_, username, _, exists := h.GetCurrentUser(c)
	if !exists {
		username = "system"
	}

	// 处理IP范围格式
	if req.IOCType == "ip" {
		// 尝试解析IP范围
		ipList, err := h.parseIPRange(req.IOC)
		if err != nil {
			h.BadRequest(c, "IP范围解析失败: "+err.Error())
			return
		}

		var createdWhitelists []models.IOCWhitelist
		var existingIPs []string

		// 为每个IP创建白名单记录
		for _, ip := range ipList {
			// 检查是否已存在
			var existingCount int64
			if err := h.db.Model(&models.IOCWhitelist{}).Where("ioc = ?", ip).Count(&existingCount).Error; err != nil {
				h.InternalServerError(c, "检查IOC是否存在失败: "+err.Error())
				return
			}
			if existingCount > 0 {
				existingIPs = append(existingIPs, ip)
				continue
			}

			whitelist := models.IOCWhitelist{
				IOC:       ip,
				IOCType:   req.IOCType,
				Remark:    req.Remark,
				CreatedBy: username,
			}
			createdWhitelists = append(createdWhitelists, whitelist)
		}

		// 批量创建记录
		if len(createdWhitelists) > 0 {
			if err := h.batchCreateWhitelists(createdWhitelists); err != nil {
				h.InternalServerError(c, "创建IOC白名单失败: "+err.Error())
				return
			}

			// 刷新白名单缓存
			if err := service.RefreshGlobalWhitelistCache(); err != nil {
				fmt.Printf("刷新IOC白名单缓存失败: %v\n", err)
			}
		}

		// 返回结果
		msg := fmt.Sprintf("成功创建 %d 条IOC白名单记录", len(createdWhitelists))
		if len(existingIPs) > 0 {
			msg += fmt.Sprintf("，跳过 %d 条已存在的记录", len(existingIPs))
		}

		h.Success(c, gin.H{
			"message":        msg,
			"created_count":  len(createdWhitelists),
			"existing_count": len(existingIPs),
			"total_ips":      len(ipList),
		})
	} else {
		// 检查IOC是否已存在
		var existingCount int64
		if err := h.db.Model(&models.IOCWhitelist{}).Where("ioc = ?", req.IOC).Count(&existingCount).Error; err != nil {
			h.InternalServerError(c, "检查IOC是否存在失败: "+err.Error())
			return
		}
		if existingCount > 0 {
			h.BadRequest(c, "该IOC白名单已存在")
			return
		}

		whitelist := models.IOCWhitelist{
			IOC:       req.IOC,
			IOCType:   req.IOCType,
			Remark:    req.Remark,
			CreatedBy: username,
		}

		// 创建记录
		if err := h.db.Create(&whitelist).Error; err != nil {
			h.InternalServerError(c, "创建IOC白名单失败: "+err.Error())
			return
		}

		// 刷新白名单缓存
		if err := service.RefreshGlobalWhitelistCache(); err != nil {
			fmt.Printf("刷新IOC白名单缓存失败: %v\n", err)
		}

		h.Success(c, gin.H{
			"message": "创建成功",
			"data":    whitelist,
		})
	}
}

// GetIOCWhitelistByID 获取IOC白名单详情
func (h *IOCHandler) GetIOCWhitelistByID(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var whitelist models.IOCWhitelist
	if err := h.db.First(&whitelist, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "IOC白名单记录不存在")
			return
		}
		h.InternalServerError(c, "获取IOC白名单详情失败: "+err.Error())
		return
	}

	h.Success(c, whitelist)
}

// UpdateIOCWhitelistRequest 更新IOC白名单请求
type UpdateIOCWhitelistRequest struct {
	IOC     *string `json:"ioc"`
	IOCType *string `json:"iocType"`
	Remark  *string `json:"remark"`
}

// UpdateIOCWhitelist 更新IOC白名单
func (h *IOCHandler) UpdateIOCWhitelist(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var whitelist models.IOCWhitelist
	if err := h.db.First(&whitelist, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "IOC白名单记录不存在")
			return
		}
		h.InternalServerError(c, "查找IOC白名单失败: "+err.Error())
		return
	}

	var req UpdateIOCWhitelistRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 构建更新数据
	updates := make(map[string]interface{})

	if req.IOC != nil && *req.IOC != whitelist.IOC {
		// 验证IOC格式
		iocType := whitelist.IOCType
		if req.IOCType != nil {
			iocType = *req.IOCType
		}
		if err := h.validateIOCFormat(*req.IOC, iocType); err != nil {
			h.BadRequest(c, err.Error())
			return
		}

		// 检查是否与其他记录冲突
		var existingCount int64
		if err := h.db.Model(&models.IOCWhitelist{}).Where("ioc = ? AND id != ?", *req.IOC, id).Count(&existingCount).Error; err != nil {
			h.InternalServerError(c, "检查IOC是否存在失败: "+err.Error())
			return
		}
		if existingCount > 0 {
			h.BadRequest(c, "该IOC白名单已存在")
			return
		}
		updates["ioc"] = *req.IOC
	}

	if req.IOCType != nil {
		if *req.IOCType != "ip" && *req.IOCType != "domain" {
			h.BadRequest(c, "无效的IOC类型，只支持ip和domain")
			return
		}
		updates["ioc_type"] = *req.IOCType
	}

	if req.Remark != nil {
		updates["remark"] = *req.Remark
	}

	// 更新记录
	if len(updates) > 0 {
		if err := h.db.Model(&whitelist).Updates(updates).Error; err != nil {
			h.InternalServerError(c, "更新IOC白名单失败: "+err.Error())
			return
		}

		// 刷新白名单缓存
		if err := service.RefreshGlobalWhitelistCache(); err != nil {
			fmt.Printf("刷新IOC白名单缓存失败: %v\n", err)
		}
	}

	h.Success(c, gin.H{"message": "更新成功"})
}

// DeleteIOCWhitelist 删除IOC白名单
func (h *IOCHandler) DeleteIOCWhitelist(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var whitelist models.IOCWhitelist
	if err := h.db.First(&whitelist, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "IOC白名单记录不存在")
			return
		}
		h.InternalServerError(c, "查找IOC白名单失败: "+err.Error())
		return
	}

	// 删除记录
	if err := h.db.Delete(&whitelist).Error; err != nil {
		h.InternalServerError(c, "删除IOC白名单失败: "+err.Error())
		return
	}

	// 刷新白名单缓存
	if err := service.RefreshGlobalWhitelistCache(); err != nil {
		fmt.Printf("刷新IOC白名单缓存失败: %v\n", err)
	}

	h.Success(c, gin.H{"message": "删除成功"})
}

// BatchDeleteIOCWhitelistRequest 批量删除IOC白名单请求
type BatchDeleteIOCWhitelistRequest struct {
	IDs []uint `json:"ids" binding:"required"`
}

// BatchDeleteIOCWhitelist 批量删除IOC白名单
func (h *IOCHandler) BatchDeleteIOCWhitelist(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req BatchDeleteIOCWhitelistRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	if len(req.IDs) == 0 {
		h.BadRequest(c, "请选择要删除的记录")
		return
	}

	// 批量删除
	result := h.db.Where("id IN ?", req.IDs).Delete(&models.IOCWhitelist{})
	if result.Error != nil {
		h.InternalServerError(c, "批量删除IOC白名单失败: "+result.Error.Error())
		return
	}

	// 刷新白名单缓存
	if err := service.RefreshGlobalWhitelistCache(); err != nil {
		fmt.Printf("刷新IOC白名单缓存失败: %v\n", err)
	}

	h.Success(c, gin.H{
		"message":       "批量删除成功",
		"deleted_count": result.RowsAffected,
	})
}

// GetIOCWhitelistCacheStats 获取IOC白名单缓存统计信息
func (h *IOCHandler) GetIOCWhitelistCacheStats(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	stats := service.GetGlobalWhitelistStats()
	h.Success(c, gin.H{
		"message": "获取缓存统计成功",
		"data":    stats,
	})
}

// RefreshIOCWhitelistCache 手动刷新IOC白名单缓存
func (h *IOCHandler) RefreshIOCWhitelistCache(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	if err := service.RefreshGlobalWhitelistCache(); err != nil {
		h.InternalServerError(c, "刷新IOC白名单缓存失败: "+err.Error())
		return
	}

	stats := service.GetGlobalWhitelistStats()
	h.Success(c, gin.H{
		"message": "刷新缓存成功",
		"data":    stats,
	})
}


