package handlers

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"sort"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"vulnerability_push/internal/models"
)

// ExportHandler 导出处理器
type ExportHandler struct {
	BaseHandler
	db *gorm.DB
}

// NewExportHandler 创建导出处理器
func NewExportHandler(db *gorm.DB) *ExportHandler {
	return &ExportHandler{
		db: db,
	}
}



// ExportFile 导出文件信息
type ExportFile struct {
	Filename   string `json:"filename"`
	CreateTime int64  `json:"createTime"`
	FileSize   int64  `json:"fileSize"`
}

// SaveExportConfigRequest 保存导出配置请求
type SaveExportConfigRequest struct {
	Frequency  string   `json:"frequency" binding:"required"`
	Severities []string `json:"severities" binding:"required"`
	WeekDay    int      `json:"weekDay"`   // 0-6 对应周日到周六
	MonthDay   int      `json:"monthDay"`  // 1-31 对应每月几号
}

// SaveExportConfig 保存导出配置
func (h *ExportHandler) SaveExportConfig(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req SaveExportConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 验证频率参数
	if req.Frequency != "weekly" && req.Frequency != "monthly" {
		h.BadRequest(c, "频率参数无效，必须是 weekly 或 monthly")
		return
	}

	if req.Frequency == "weekly" && (req.WeekDay < 0 || req.WeekDay > 6) {
		h.BadRequest(c, "周几参数无效，必须是 0-6 之间的整数")
		return
	}

	if req.Frequency == "monthly" && (req.MonthDay < 1 || req.MonthDay > 31) {
		h.BadRequest(c, "月几号参数无效，必须是 1-31 之间的整数")
		return
	}

	// 获取现有配置
	var existingConfig models.ExportConfig
	result := h.db.First(&existingConfig)

	now := time.Now().Unix()

	if result.Error != nil {
		// 创建新配置
		newConfig := models.ExportConfig{
			Frequency: req.Frequency,
			WeekDay:   req.WeekDay,
			MonthDay:  req.MonthDay,
			LastRun:   0,
			CreatedAt: now,
			UpdatedAt: now,
		}
		// 设置严重程度
		newConfig.SetSeverities(req.Severities)

		if err := h.db.Create(&newConfig).Error; err != nil {
			h.InternalServerError(c, "保存导出配置失败: "+err.Error())
			return
		}
	} else {
		// 更新现有配置
		existingConfig.Frequency = req.Frequency
		existingConfig.WeekDay = req.WeekDay
		existingConfig.MonthDay = req.MonthDay
		existingConfig.SetSeverities(req.Severities)
		existingConfig.UpdatedAt = now

		if err := h.db.Save(&existingConfig).Error; err != nil {
			h.InternalServerError(c, "更新导出配置失败: "+err.Error())
			return
		}
	}

	h.SuccessWithMessage(c, "保存导出配置成功", nil)
}

// GetExportConfig 获取导出配置
func (h *ExportHandler) GetExportConfig(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var config models.ExportConfig
	result := h.db.First(&config)

	if result.Error != nil {
		// 如果没有配置，返回默认配置
		config = models.ExportConfig{
			Frequency: "weekly",
			WeekDay:   1, // 默认周一
			MonthDay:  1, // 默认每月1号
			LastRun:   0,
			CreatedAt: 0,
			UpdatedAt: 0,
		}
		config.SetSeverities([]string{"严重", "高危", "中危", "低危", "信息"})
	}

	// 构建响应
	responseData := map[string]interface{}{
		"id":         config.ID,
		"frequency":  config.Frequency,
		"severities": config.GetSeverities(),
		"weekDay":    config.WeekDay,
		"monthDay":   config.MonthDay,
		"lastRun":    config.LastRun,
		"createdAt":  config.CreatedAt,
		"updatedAt":  config.UpdatedAt,
	}

	h.SuccessWithMessage(c, "获取导出配置成功", responseData)
}

// GetExportFiles 获取导出文件列表
func (h *ExportHandler) GetExportFiles(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	// 创建导出目录（如果不存在）
	exportDir := "./exports"
	if _, err := os.Stat(exportDir); os.IsNotExist(err) {
		if err := os.MkdirAll(exportDir, 0755); err != nil {
			h.InternalServerError(c, "创建导出目录失败: "+err.Error())
			return
		}
	}

	// 读取目录中的文件
	files, err := ioutil.ReadDir(exportDir)
	if err != nil {
		h.InternalServerError(c, "读取导出文件列表失败: "+err.Error())
		return
	}

	// 转换为导出文件信息
	var exportFiles []ExportFile
	for _, file := range files {
		if file.IsDir() {
			continue // 跳过目录
		}

		if filepath.Ext(file.Name()) != ".xlsx" {
			continue // 只处理Excel文件
		}

		exportFiles = append(exportFiles, ExportFile{
			Filename:   file.Name(),
			CreateTime: file.ModTime().Unix(),
			FileSize:   file.Size(),
		})
	}

	// 按时间倒序排序
	sort.Slice(exportFiles, func(i, j int) bool {
		return exportFiles[i].CreateTime > exportFiles[j].CreateTime
	})

	h.SuccessWithMessage(c, "获取导出文件列表成功", exportFiles)
}

// DownloadExportFile 下载导出文件
func (h *ExportHandler) DownloadExportFile(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	filename := c.Param("filename")

	// 验证文件名
	if filename == "" {
		h.BadRequest(c, "文件名不能为空")
		return
	}

	// 构建文件路径
	filePath := filepath.Join("./exports", filename)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		h.NotFound(c, "文件不存在")
		return
	}

	// 设置响应头
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", fmt.Sprintf(`attachment; filename="%s"`, filename))
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Expires", "0")
	c.Header("Cache-Control", "must-revalidate")
	c.Header("Pragma", "public")

	// 发送文件
	c.File(filePath)
}

// DeleteExportFile 删除导出文件
func (h *ExportHandler) DeleteExportFile(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	filename := c.Param("filename")

	// 验证文件名
	if filename == "" {
		h.BadRequest(c, "文件名不能为空")
		return
	}

	// 构建文件路径
	filePath := filepath.Join("./exports", filename)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		h.NotFound(c, "文件不存在")
		return
	}

	// 删除文件
	if err := os.Remove(filePath); err != nil {
		h.InternalServerError(c, "删除文件失败: "+err.Error())
		return
	}

	h.SuccessWithMessage(c, "文件删除成功", nil)
}
