package handlers

import (
	"time"
	"github.com/gin-gonic/gin"
	"vulnerability_push/internal/models"
)

// ========================================
// IOC数据采集模块 (Data Collection Module)
// 负责从各种数据接口采集原始数据，处理和清洗数据，存储为源数据
// ========================================

// GetIOCSourceDataRequest 获取IOC源数据请求 (重命名：原IOCIntelligenceData -> IOCSourceData)
type GetIOCSourceDataRequest struct {
	Page        int    `form:"page"`
	PageSize    int    `form:"page_size"`
	AttackIP     string `form:"attack_ip"`
	VictimIP     string `form:"victim_ip"`
	SourceLabel  string `form:"source_label"`
	Category     string `form:"category"`
	OrderBy      string `form:"order_by"`      // 排序字段
	OrderDir     string `form:"order_dir"`     // 排序方向 asc/desc
}

// NormalizePagination 标准化分页参数
func (req *GetIOCSourceDataRequest) NormalizePagination() {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}
}

// GenerateIOCSourceDataRequest 生成IOC源数据请求 (从数据接口采集)
type GenerateIOCSourceDataRequest struct {
	Host      string `json:"host" binding:"required"`
	UserKey   string `json:"user_key" binding:"required"`
	StartTime int64  `json:"start_time" binding:"required"`
	EndTime   int64  `json:"end_time" binding:"required"`
}

// BatchDeleteIOCSourceDataRequest 批量删除IOC源数据请求
type BatchDeleteIOCSourceDataRequest struct {
	IDs []uint `json:"ids" binding:"required,min=1"`
}

// IOCSourceDataStatsResponse IOC源数据统计响应
type IOCSourceDataStatsResponse struct {
	TotalRecords    int64            `json:"totalRecords"`
	TodayRecords    int64            `json:"todayRecords"`
	SourceLabels    map[string]int64 `json:"sourceLabels"`
	Categories      map[string]int64 `json:"categories"`
	TopAttackIPs    []AttackIPStat   `json:"topAttackIPs"`
	ThreatScoreAvg  float64          `json:"threatScoreAvg"`
}

// AttackIPStat 攻击IP统计
type AttackIPStat struct {
	AttackIP    string  `json:"attackIp"`
	AttackCount int     `json:"attackCount"`
	ThreatScore float64 `json:"threatScore"`
}

// GetIOCSourceData 获取IOC源数据列表 (数据采集模块的核心接口)
func (h *IOCHandler) GetIOCSourceData(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req GetIOCSourceDataRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 标准化分页参数
	req.NormalizePagination()
	if req.PageSize <= 0 {
		req.PageSize = 20 // IOC源数据默认每页20条
	}

	// 构建查询
	query := h.db.Model(&models.IOCIntelligenceData{})

	// 应用过滤条件
	if req.AttackIP != "" {
		query = query.Where("attack_ip LIKE ?", "%"+req.AttackIP+"%")
	}
	if req.VictimIP != "" {
		query = query.Where("victim_ip LIKE ?", "%"+req.VictimIP+"%")
	}
	if req.SourceLabel != "" {
		query = query.Where("source_label LIKE ?", "%"+req.SourceLabel+"%")
	}
	if req.Category != "" {
		query = query.Where("category LIKE ?", "%"+req.Category+"%")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取IOC源数据总数失败: "+err.Error())
		return
	}

	// 应用排序和分页
	var data []models.IOCIntelligenceData
	offset := (req.Page - 1) * req.PageSize

	// 构建排序条件
	orderBy := "created_at"
	orderDir := "desc"

	if req.OrderBy != "" {
		// 验证排序字段
		validOrderFields := map[string]bool{
			"created_at":        true,
			"attack_count":      true,
			"threat_score":      true,
			"first_attack_time": true,
			"last_attack_time":  true,
			"attack_ip":         true,
			"victim_ip":         true,
			"source_label":      true,
			"category":          true,
		}
		if validOrderFields[req.OrderBy] {
			orderBy = req.OrderBy
		}
	}

	if req.OrderDir != "" && (req.OrderDir == "asc" || req.OrderDir == "desc") {
		orderDir = req.OrderDir
	}

	// 默认排序：新更新的在前，命中次数高的在前
	orderClause := orderBy + " " + orderDir
	if orderBy != "created_at" {
		orderClause += ", created_at DESC"
	}
	if orderBy != "attack_count" {
		orderClause += ", attack_count DESC"
	}

	if err := query.Order(orderClause).Offset(offset).Limit(req.PageSize).Find(&data).Error; err != nil {
		h.InternalServerError(c, "获取IOC源数据列表失败: "+err.Error())
		return
	}

	h.PaginatedSuccess(c, data, total, req.Page, req.PageSize)
}

// GenerateIOCSourceData 从数据接口生成IOC源数据 (数据采集的核心功能)
func (h *IOCHandler) GenerateIOCSourceData(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req GenerateIOCSourceDataRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// TODO: 实现IOC源数据生成逻辑
	// 这里应该调用数据接口(如CCCC黑科技接口)获取原始数据并处理成源数据

	h.Success(c, gin.H{"message": "IOC源数据生成成功"})
}

// BatchDeleteIOCSourceData 批量删除IOC源数据
func (h *IOCHandler) BatchDeleteIOCSourceData(c *gin.Context) {
	// 检查管理员权限
	if !h.RequireAdmin(c) {
		return
	}

	var req BatchDeleteIOCSourceDataRequest
	if err := h.BindJSON(c, &req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 检查所有IOC源数据是否存在
	var count int64
	if err := h.db.Model(&models.IOCIntelligenceData{}).Where("id IN ?", req.IDs).Count(&count).Error; err != nil {
		h.InternalServerError(c, "查询IOC源数据失败: "+err.Error())
		return
	}

	if int(count) != len(req.IDs) {
		h.BadRequest(c, "部分IOC源数据不存在")
		return
	}

	// 批量删除IOC源数据
	result := h.db.Where("id IN ?", req.IDs).Delete(&models.IOCIntelligenceData{})
	if result.Error != nil {
		h.InternalServerError(c, "批量删除IOC源数据失败: "+result.Error.Error())
		return
	}

	h.SuccessWithMessage(c, "批量删除成功", map[string]interface{}{
		"deletedCount": result.RowsAffected,
	})
}

// ClearUUIDRecords 清除UUID去重记录
func (h *IOCHandler) ClearUUIDRecords(c *gin.Context) {
	// 检查管理员权限
	if !h.RequireAdmin(c) {
		return
	}

	// 清除所有UUID记录
	result := h.db.Where("1 = 1").Delete(&models.ProcessedUUID{})
	if result.Error != nil {
		h.InternalServerError(c, "清除UUID记录失败: "+result.Error.Error())
		return
	}

	h.SuccessWithMessage(c, "UUID记录清除成功", map[string]interface{}{
		"deletedCount": result.RowsAffected,
	})
}

// GetIOCSourceDataStats 获取IOC源数据统计信息
func (h *IOCHandler) GetIOCSourceDataStats(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	// 统计总记录数
	var totalRecords int64
	if err := h.db.Model(&models.IOCIntelligenceData{}).Count(&totalRecords).Error; err != nil {
		h.InternalServerError(c, "获取总记录数失败: "+err.Error())
		return
	}

	// 统计今日记录数
	today := time.Now().Format("2006-01-02")
	var todayRecords int64
	if err := h.db.Model(&models.IOCIntelligenceData{}).
		Where("DATE(created_at) = ?", today).
		Count(&todayRecords).Error; err != nil {
		h.InternalServerError(c, "获取今日记录数失败: "+err.Error())
		return
	}

	// 统计来源标签分布（按命中次数统计）
	sourceLabels := make(map[string]int64)
	var sourceLabelStats []struct {
		SourceLabel string `json:"source_label"`
		TotalHits   int64  `json:"total_hits"`
	}
	if err := h.db.Model(&models.IOCIntelligenceData{}).
		Select("source_label, SUM(attack_count) as total_hits").
		Where("source_label != '' AND source_label IS NOT NULL").
		Group("source_label").
		Order("total_hits DESC").
		Scan(&sourceLabelStats).Error; err != nil {
		h.InternalServerError(c, "获取来源标签分布失败: "+err.Error())
		return
	}
	for _, stat := range sourceLabelStats {
		sourceLabels[stat.SourceLabel] = stat.TotalHits
	}

	// 统计攻击类别分布
	categories := make(map[string]int64)
	var categoryStats []struct {
		Category string `json:"category"`
		Count    int64  `json:"count"`
	}
	if err := h.db.Model(&models.IOCIntelligenceData{}).
		Select("category, COUNT(*) as count").
		Where("category != '' AND category IS NOT NULL").
		Group("category").
		Scan(&categoryStats).Error; err != nil {
		h.InternalServerError(c, "获取攻击类别分布失败: "+err.Error())
		return
	}
	for _, stat := range categoryStats {
		categories[stat.Category] = stat.Count
	}

	// 统计TOP攻击IP
	var topAttackIPs []AttackIPStat
	if err := h.db.Model(&models.IOCIntelligenceData{}).
		Select("attack_ip, SUM(attack_count) as total_attacks, COUNT(*) as record_count").
		Where("attack_ip != '' AND attack_ip IS NOT NULL").
		Group("attack_ip").
		Order("total_attacks DESC").
		Limit(10).
		Scan(&topAttackIPs).Error; err != nil {
		h.InternalServerError(c, "获取TOP攻击IP失败: "+err.Error())
		return
	}

	// 计算平均威胁评分
	var threatScoreAvg float64
	if err := h.db.Model(&models.IOCIntelligenceData{}).
		Select("AVG(threat_score) as avg_score").
		Where("threat_score > 0").
		Scan(&threatScoreAvg).Error; err != nil {
		h.InternalServerError(c, "获取平均威胁评分失败: "+err.Error())
		return
	}

	response := IOCSourceDataStatsResponse{
		TotalRecords:   totalRecords,
		TodayRecords:   todayRecords,
		SourceLabels:   sourceLabels,
		Categories:     categories,
		TopAttackIPs:   topAttackIPs,
		ThreatScoreAvg: threatScoreAvg,
	}

	h.Success(c, response)
}

// GetIOCSourceLabels 获取所有不同的来源标签
func (h *IOCHandler) GetIOCSourceLabels(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var labels []string
	if err := h.db.Model(&models.IOCIntelligenceData{}).
		Distinct("source_label").
		Where("source_label != '' AND source_label IS NOT NULL").
		Pluck("source_label", &labels).Error; err != nil {
		h.InternalServerError(c, "获取来源标签列表失败: "+err.Error())
		return
	}

	h.Success(c, labels)
}
