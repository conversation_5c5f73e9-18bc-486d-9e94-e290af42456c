package handlers

import (
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"vulnerability_push/internal/models"
	"vulnerability_push/internal/service"
)

// ThreatIntelligenceInterfaceHandler 威胁情报接口处理器
type ThreatIntelligenceInterfaceHandler struct {
	*BaseHandler
	db *gorm.DB
}

// NewThreatIntelligenceInterfaceHandler 创建威胁情报接口处理器
func NewThreatIntelligenceInterfaceHandler(db *gorm.DB) *ThreatIntelligenceInterfaceHandler {
	return &ThreatIntelligenceInterfaceHandler{
		BaseHandler: NewBaseHandler(),
		db:          db,
	}
}

// GetThreatIntelligenceInterfacesRequest 获取威胁情报接口请求
type GetThreatIntelligenceInterfacesRequest struct {
	service.PaginationRequest
	Name   string `form:"name"`
	Type   string `form:"type"`
	Status string `form:"status"`
}

// CreateThreatIntelligenceInterfaceRequest 创建威胁情报接口请求
type CreateThreatIntelligenceInterfaceRequest struct {
	Name        string `json:"name" binding:"required"`
	Type        string `json:"type" binding:"required"`
	Description string `json:"description"`
	Config      string `json:"config" binding:"required"`
	Status      string `json:"status"`
}

// UpdateThreatIntelligenceInterfaceRequest 更新威胁情报接口请求
type UpdateThreatIntelligenceInterfaceRequest struct {
	Name        *string `json:"name"`
	Type        *string `json:"type"`
	Description *string `json:"description"`
	Config      *string `json:"config"`
	Status      *string `json:"status"`
}

// TestThreatIntelligenceInterfaceRequest 测试威胁情报接口请求
type TestThreatIntelligenceInterfaceRequest struct {
	IOC     string `json:"ioc" binding:"required"`
	IOCType string `json:"ioc_type" binding:"required"`
}

// BatchDeleteRequest 批量删除请求
type BatchDeleteRequest struct {
	IDs []uint `json:"ids" binding:"required"`
}

// GetThreatIntelligenceInterfaces 获取威胁情报接口列表
func (h *ThreatIntelligenceInterfaceHandler) GetThreatIntelligenceInterfaces(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req GetThreatIntelligenceInterfacesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	query := h.db.Model(&models.ThreatIntelligenceInterface{})

	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.Type != "" {
		query = query.Where("type = ?", req.Type)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取威胁情报接口总数失败: "+err.Error())
		return
	}

	// 获取列表数据
	var interfaces []models.ThreatIntelligenceInterface
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&interfaces).Error; err != nil {
		h.InternalServerError(c, "获取威胁情报接口列表失败: "+err.Error())
		return
	}

	h.Success(c, gin.H{
		"list":  interfaces,
		"total": total,
		"pagination": gin.H{
			"page":      req.Page,
			"page_size": req.PageSize,
			"total":     total,
		},
	})
}

// GetThreatIntelligenceInterface 获取威胁情报接口详情
func (h *ThreatIntelligenceInterfaceHandler) GetThreatIntelligenceInterface(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var interface_ models.ThreatIntelligenceInterface
	if err := h.db.First(&interface_, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "威胁情报接口不存在")
			return
		}
		h.InternalServerError(c, "获取威胁情报接口详情失败: "+err.Error())
		return
	}

	h.Success(c, interface_)
}

// CreateThreatIntelligenceInterface 创建威胁情报接口
func (h *ThreatIntelligenceInterfaceHandler) CreateThreatIntelligenceInterface(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req CreateThreatIntelligenceInterfaceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 验证接口类型
	supportedTypes := models.GetSupportedThreatIntelligenceTypes()
	isValidType := false
	for _, t := range supportedTypes {
		if t == req.Type {
			isValidType = true
			break
		}
	}
	if !isValidType {
		h.BadRequest(c, "不支持的威胁情报接口类型")
		return
	}

	// 设置默认状态
	if req.Status == "" {
		req.Status = "disabled"
	}

	// 创建威胁情报接口
	interface_ := models.ThreatIntelligenceInterface{
		Name:        req.Name,
		Type:        req.Type,
		Description: req.Description,
		Config:      req.Config,
		Status:      req.Status,
	}

	// 验证配置
	if err := interface_.ValidateConfig(); err != nil {
		h.BadRequest(c, "配置验证失败: "+err.Error())
		return
	}

	if err := h.db.Create(&interface_).Error; err != nil {
		h.InternalServerError(c, "创建威胁情报接口失败: "+err.Error())
		return
	}

	h.Success(c, interface_)
}

// UpdateThreatIntelligenceInterface 更新威胁情报接口
func (h *ThreatIntelligenceInterfaceHandler) UpdateThreatIntelligenceInterface(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var req UpdateThreatIntelligenceInterfaceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 获取现有威胁情报接口
	var interface_ models.ThreatIntelligenceInterface
	if err := h.db.First(&interface_, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "威胁情报接口不存在")
			return
		}
		h.InternalServerError(c, "获取威胁情报接口失败: "+err.Error())
		return
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Type != nil {
		// 验证接口类型
		supportedTypes := models.GetSupportedThreatIntelligenceTypes()
		isValidType := false
		for _, t := range supportedTypes {
			if t == *req.Type {
				isValidType = true
				break
			}
		}
		if !isValidType {
			h.BadRequest(c, "不支持的威胁情报接口类型")
			return
		}
		updates["type"] = *req.Type
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Config != nil {
		updates["config"] = *req.Config
		// 如果更新了配置，需要验证
		tempInterface := interface_
		tempInterface.Config = *req.Config
		if req.Type != nil {
			tempInterface.Type = *req.Type
		}
		if err := tempInterface.ValidateConfig(); err != nil {
			h.BadRequest(c, "配置验证失败: "+err.Error())
			return
		}
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}

	if err := h.db.Model(&interface_).Updates(updates).Error; err != nil {
		h.InternalServerError(c, "更新威胁情报接口失败: "+err.Error())
		return
	}

	h.Success(c, gin.H{"message": "威胁情报接口更新成功"})
}

// DeleteThreatIntelligenceInterface 删除威胁情报接口
func (h *ThreatIntelligenceInterfaceHandler) DeleteThreatIntelligenceInterface(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	// 检查威胁情报接口是否存在
	var interface_ models.ThreatIntelligenceInterface
	if err := h.db.First(&interface_, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "威胁情报接口不存在")
			return
		}
		h.InternalServerError(c, "获取威胁情报接口失败: "+err.Error())
		return
	}

	if err := h.db.Delete(&interface_).Error; err != nil {
		h.InternalServerError(c, "删除威胁情报接口失败: "+err.Error())
		return
	}

	h.Success(c, gin.H{"message": "威胁情报接口删除成功"})
}

// BatchDeleteThreatIntelligenceInterfaces 批量删除威胁情报接口
func (h *ThreatIntelligenceInterfaceHandler) BatchDeleteThreatIntelligenceInterfaces(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req BatchDeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	if len(req.IDs) == 0 {
		h.BadRequest(c, "请选择要删除的威胁情报接口")
		return
	}

	if err := h.db.Where("id IN ?", req.IDs).Delete(&models.ThreatIntelligenceInterface{}).Error; err != nil {
		h.InternalServerError(c, "批量删除威胁情报接口失败: "+err.Error())
		return
	}

	h.Success(c, gin.H{
		"message": "批量删除威胁情报接口成功",
		"count":   len(req.IDs),
	})
}

// TestThreatIntelligenceInterface 测试威胁情报接口连接
func (h *ThreatIntelligenceInterfaceHandler) TestThreatIntelligenceInterface(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var req TestThreatIntelligenceInterfaceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 获取威胁情报接口
	var interface_ models.ThreatIntelligenceInterface
	if err := h.db.First(&interface_, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "威胁情报接口不存在")
			return
		}
		h.InternalServerError(c, "获取威胁情报接口失败: "+err.Error())
		return
	}

	// 根据接口类型进行测试
	var result interface{}
	var testErr error

	switch interface_.Type {
	case "tjun":
		result, testErr = h.testTJUNInterface(&interface_, req.IOC, req.IOCType)
	case "weibu":
		result, testErr = h.testWeibuInterface(&interface_, req.IOC, req.IOCType)
	default:
		h.BadRequest(c, "不支持的威胁情报接口类型")
		return
	}

	if testErr != nil {
		h.BadRequest(c, "测试失败: "+testErr.Error())
		return
	}

	h.Success(c, gin.H{
		"message": "测试成功",
		"data":    result,
	})
}

// testTJUNInterface 测试天际友盟接口
func (h *ThreatIntelligenceInterfaceHandler) testTJUNInterface(interface_ *models.ThreatIntelligenceInterface, ioc, iocType string) (interface{}, error) {
	// 获取配置
	config, err := interface_.GetTJUNConfig()
	if err != nil {
		return nil, err
	}

	// 创建临时的天际友盟服务配置
	tjunConfig := service.TJUNConfig{
		Host:      config.Host,
		AppKey:    config.AppKey,
		AppSecret: config.AppSecret,
		Token:     config.Token,
		Timeout:   time.Duration(config.Timeout) * time.Second,
	}

	// 创建临时服务实例
	tjunService := service.NewTJUNIntelligenceService(h.db, tjunConfig)

	// 执行查询
	result, err := tjunService.QueryIOC(service.TJUNQueryRequest{
		IOC:     ioc,
		IOCType: iocType,
	})

	return result, err
}

// testWeibuInterface 测试微步威胁情报接口
func (h *ThreatIntelligenceInterfaceHandler) testWeibuInterface(interface_ *models.ThreatIntelligenceInterface, ioc, iocType string) (interface{}, error) {
	// 获取配置
	_, err := interface_.GetWeibuConfig()
	if err != nil {
		return nil, err
	}

	// 创建临时的微步威胁情报服务实例
	weibuService := service.NewWeibuIntelligenceService(h.db)

	// 执行查询（目前微步只支持IP查询）
	if iocType != "ip" {
		return nil, &models.CustomError{
			Code:    "UNSUPPORTED_IOC_TYPE",
			Message: "微步威胁情报目前只支持IP类型查询",
		}
	}

	result, err := weibuService.QueryIOC(service.WeibuQueryRequest{
		IOC: ioc,
	})

	return result, err
}

// RegisterRoutes 注册路由
func (h *ThreatIntelligenceInterfaceHandler) RegisterRoutes(router *gin.RouterGroup) {
	// 威胁情报接口管理路由
	threatIntelRoutes := router.Group("/threat-intelligence-interfaces")
	{
		threatIntelRoutes.GET("", h.GetThreatIntelligenceInterfaces)                    // 获取威胁情报接口列表
		threatIntelRoutes.POST("", h.CreateThreatIntelligenceInterface)                // 创建威胁情报接口
		threatIntelRoutes.GET("/:id", h.GetThreatIntelligenceInterface)                // 获取威胁情报接口详情
		threatIntelRoutes.PUT("/:id", h.UpdateThreatIntelligenceInterface)             // 更新威胁情报接口
		threatIntelRoutes.DELETE("/:id", h.DeleteThreatIntelligenceInterface)          // 删除威胁情报接口
		threatIntelRoutes.POST("/batch-delete", h.BatchDeleteThreatIntelligenceInterfaces) // 批量删除威胁情报接口
		threatIntelRoutes.POST("/:id/test", h.TestThreatIntelligenceInterface)         // 测试威胁情报接口
	}
}
