package handlers

import (
	"errors"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"vulnerability_push/internal/models"
)

// VulnerabilityHandler 漏洞处理器
type VulnerabilityHandler struct {
	*BaseHandler
	db *gorm.DB
	// vulnerabilityService service.VulnerabilityServiceInterface // 注入漏洞服务
}

// NewVulnerabilityHandler 创建漏洞处理器
func NewVulnerabilityHandler(db *gorm.DB) *VulnerabilityHandler {
	return &VulnerabilityHandler{
		BaseHandler: NewBaseHandler(),
		db:          db,
	}
}

// GetVulnerabilitiesRequest 获取漏洞列表请求
type GetVulnerabilitiesRequest struct {
	Page      int    `form:"page" binding:"required,min=1"`
	PageSize  int    `form:"pageSize" binding:"required,min=5,max=100"`
	Keyword   string `form:"keyword"`
	Severity  string `form:"severity"`
	Source    string `form:"source"`
	StartDate string `form:"startDate"`
	EndDate   string `form:"endDate"`
	SortBy    string `form:"sortBy"`
	SortOrder string `form:"sortOrder"`
}

// GetVulnerabilities 获取漏洞列表
func (h *VulnerabilityHandler) GetVulnerabilities(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req GetVulnerabilitiesRequest
	if err := h.BindQuery(c, &req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 构建查询
	query := h.db.Model(&models.Vulnerability{})

	// 关键词搜索
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		query = query.Where("name LIKE ? OR vuln_id LIKE ? OR description LIKE ? OR tags LIKE ?", 
			keyword, keyword, keyword, keyword)
	}

	// 严重程度过滤
	if req.Severity != "" {
		query = query.Where("severity = ?", req.Severity)
	}

	// 来源过滤
	if req.Source != "" {
		// 根据中文名称映射到对应的URL关键词
		sourceKeyword := ""
		switch req.Source {
		case "长亭漏洞库":
			sourceKeyword = "chaitin"
		case "奇安信威胁情报中心":
			sourceKeyword = "qianxin"
		case "阿里云漏洞库":
			sourceKeyword = "aliyun"
		case "微步威胁情报":
			sourceKeyword = "threatbook"
		case "Seebug漏洞平台":
			sourceKeyword = "seebug"
		case "启明星辰漏洞通告":
			sourceKeyword = "venustech"
		case "OSCS情报预警":
			sourceKeyword = "oscs"
		case "NVD漏洞数据库":
			sourceKeyword = "nvd"
		default:
			// 如果是其他来源，直接使用精确匹配
			sourceKeyword = req.Source
		}

		if sourceKeyword != "" {
			if sourceKeyword == req.Source {
				// 精确匹配
				query = query.Where("source = ?", req.Source)
			} else {
				// 模糊匹配
				query = query.Where("source LIKE ?", "%"+sourceKeyword+"%")
			}
		}
	}

	// 日期范围过滤
	if req.StartDate != "" {
		query = query.Where("disclosure_date >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		query = query.Where("disclosure_date <= ?", req.EndDate)
	}

	// 排序
	orderBy := "created_at DESC" // 默认排序
	if req.SortBy != "" {
		direction := "ASC"
		if req.SortOrder == "desc" {
			direction = "DESC"
		}
		
		// 验证排序字段
		validSortFields := map[string]bool{
			"name":           true,
			"severity":       true,
			"disclosure_date": true,
			"created_at":     true,
			"source":         true,
		}
		
		if validSortFields[req.SortBy] {
			orderBy = req.SortBy + " " + direction
		}
	}
	query = query.Order(orderBy)

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取漏洞总数失败: "+err.Error())
		return
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	var vulnerabilities []models.Vulnerability
	if err := query.Offset(offset).Limit(req.PageSize).Find(&vulnerabilities).Error; err != nil {
		h.InternalServerError(c, "获取漏洞列表失败: "+err.Error())
		return
	}

	// 转换响应格式
	var responseData []map[string]interface{}
	for _, vuln := range vulnerabilities {
		tags := []string{}
		if vuln.Tags != "" {
			tags = strings.Split(vuln.Tags, ",")
		}
		
		references := []string{}
		if vuln.References != "" {
			references = strings.Split(vuln.References, ",")
		}

		responseData = append(responseData, map[string]interface{}{
			"id":             vuln.ID,
			"name":           vuln.Name,
			"vulnId":         vuln.VulnID,
			"severity":       vuln.Severity,
			"tags":           tags,
			"disclosureDate": vuln.DisclosureDate,
			"source":         vuln.Source,
			"description":    vuln.Description,
			"references":     references,
			"remediation":    vuln.Remediation,
			"createdAt":      vuln.CreatedAt,
		})
	}

	h.Success(c, map[string]interface{}{
		"list": responseData,
		"pagination": map[string]interface{}{
			"total":    total,
			"page":     req.Page,
			"pageSize": req.PageSize,
		},
	})
}

// GetVulnerabilityRequest 获取单个漏洞请求
type GetVulnerabilityRequest struct {
	ID uint `uri:"id" binding:"required"`
}

// GetVulnerability 获取单个漏洞
func (h *VulnerabilityHandler) GetVulnerability(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req GetVulnerabilityRequest
	if err := c.ShouldBindUri(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	var vulnerability models.Vulnerability
	if err := h.db.First(&vulnerability, req.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			h.NotFound(c, "漏洞不存在")
			return
		}
		h.InternalServerError(c, "获取漏洞失败: "+err.Error())
		return
	}

	// 转换响应格式
	tags := []string{}
	if vulnerability.Tags != "" {
		tags = strings.Split(vulnerability.Tags, ",")
	}
	
	references := []string{}
	if vulnerability.References != "" {
		references = strings.Split(vulnerability.References, ",")
	}

	responseData := map[string]interface{}{
		"id":             vulnerability.ID,
		"name":           vulnerability.Name,
		"vulnId":         vulnerability.VulnID,
		"severity":       vulnerability.Severity,
		"tags":           tags,
		"disclosureDate": vulnerability.DisclosureDate,
		"source":         vulnerability.Source,
		"description":    vulnerability.Description,
		"references":     references,
		"remediation":    vulnerability.Remediation,
		"createdAt":      vulnerability.CreatedAt,
	}

	h.Success(c, responseData)
}

// CreateVulnerabilityRequest 创建漏洞请求
type CreateVulnerabilityRequest struct {
	Name           string   `json:"name" binding:"required,max=200"`
	VulnID         string   `json:"vulnId" binding:"required,max=100"`
	Severity       string   `json:"severity" binding:"required,oneof=低危 中危 高危 严重"`
	Tags           []string `json:"tags"`
	DisclosureDate string   `json:"disclosureDate" binding:"required"`
	Source         string   `json:"source" binding:"required,max=100"`
	Description    string   `json:"description" binding:"required"`
	References     []string `json:"references"`
	Remediation    string   `json:"remediation" binding:"required"`
	PushReason     string   `json:"pushReason"`
}

// CreateVulnerability 创建漏洞
func (h *VulnerabilityHandler) CreateVulnerability(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req CreateVulnerabilityRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 检查漏洞ID是否已存在
	var existingVuln models.Vulnerability
	if err := h.db.Where("vuln_id = ?", req.VulnID).First(&existingVuln).Error; err == nil {
		h.BadRequest(c, "漏洞ID已存在")
		return
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		h.InternalServerError(c, "检查漏洞ID失败: "+err.Error())
		return
	}

	// 创建漏洞对象
	vulnerability := models.Vulnerability{
		Name:           req.Name,
		VulnID:         req.VulnID,
		Severity:       req.Severity,
		Tags:           strings.Join(req.Tags, ","),
		DisclosureDate: req.DisclosureDate,
		Source:         req.Source,
		Description:    req.Description,
		References:     strings.Join(req.References, ","),
		Remediation:    req.Remediation,
		PushReason:     req.PushReason,
		CreatedAt:      time.Now().Unix(),
	}

	// 保存到数据库
	if err := h.db.Create(&vulnerability).Error; err != nil {
		h.InternalServerError(c, "创建漏洞失败: "+err.Error())
		return
	}

	// 尝试自动推送漏洞
	go h.autoTriggerPush(&vulnerability)

	// 返回创建的漏洞信息
	responseData := map[string]interface{}{
		"id":             vulnerability.ID,
		"name":           vulnerability.Name,
		"vulnId":         vulnerability.VulnID,
		"severity":       vulnerability.Severity,
		"tags":           strings.Split(vulnerability.Tags, ","),
		"disclosureDate": vulnerability.DisclosureDate,
		"source":         vulnerability.Source,
		"description":    vulnerability.Description,
		"references":     strings.Split(vulnerability.References, ","),
		"remediation":    vulnerability.Remediation,
		"createdAt":      vulnerability.CreatedAt,
	}

	h.SuccessWithMessage(c, "漏洞创建成功", responseData)
}
